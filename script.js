// === RedFox Portfolio Interactivity ===

// Constants
const ANIMATION_THRESHOLD = 0.15;
const SKILL_ANIMATION_THRESHOLD = 0.85;
const ANIMATION_DURATION = 1000;
const ANIMATION_EASING = "cubic-bezier(0.4,1.4,0.6,1)";
const GRADIENT_COLORS = {
  start: "#FF7F2A",
  end: "#FF9900",
};

// Utility Functions
const createElement = (tag, className, attributes = {}) => {
  const element = document.createElement(tag);
  if (className) element.className = className;
  Object.entries(attributes).forEach(([key, value]) => {
    element.setAttribute(key, value);
  });
  return element;
};

// Navigation Module
const initNavigation = () => {
  document.querySelectorAll(".navbar a").forEach((link) => {
    link.addEventListener("click", function (e) {
      const targetId = this.getAttribute("href").slice(1);
      const target = document.getElementById(targetId);
      if (target) {
        e.preventDefault();
        target.scrollIntoView({ behavior: "smooth" });
      }
    });
  });
};

// Section Animation Module
const initSectionAnimations = () => {
  const sectionObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("visible");
        }
      });
    },
    { threshold: ANIMATION_THRESHOLD }
  );

  document.querySelectorAll(".section").forEach((section) => {
    sectionObserver.observe(section);
  });
};

// Skills Animation Module
const initSkillsAnimation = () => {
  const skillSection = document.getElementById("competences");
  let skillsAnimated = false;

  const animateSkillBar = (bar) => {
    const level = bar.getAttribute("data-level");
    const inner = createElement("div", "bar-inner");

    Object.assign(inner.style, {
      position: "absolute",
      left: "0",
      top: "0",
      height: "100%",
      width: "0%",
      background: `linear-gradient(90deg, ${GRADIENT_COLORS.start}, ${GRADIENT_COLORS.end})`,
      boxShadow: `0 0 8px ${GRADIENT_COLORS.end}`,
      borderRadius: "4px",
      transition: "width 1.2s cubic-bezier(0.4, 1.4, 0.6, 1)",
    });

    bar.innerHTML = "";
    bar.appendChild(inner);

    setTimeout(() => {
      inner.style.width = `${level}%`;
    }, 200);
  };

  const checkAndAnimateSkills = () => {
    if (
      !skillsAnimated &&
      skillSection.getBoundingClientRect().top <
        window.innerHeight * SKILL_ANIMATION_THRESHOLD
    ) {
      document.querySelectorAll(".skill-bar").forEach(animateSkillBar);
      skillsAnimated = true;
    }
  };

  window.addEventListener("scroll", checkAndAnimateSkills);
  window.addEventListener("load", checkAndAnimateSkills);
};

// Contact Form Module
const initContactForm = () => {
  const contactForm = document.querySelector(".contact-form");
  const foxMascotContact = document.getElementById("fox-mascot-contact");

  if (!contactForm || !foxMascotContact) return;

  contactForm.addEventListener("submit", function (e) {
    e.preventDefault();

    foxMascotContact.animate(
      [
        { transform: "translateY(0)" },
        { transform: "translateY(-18px)" },
        { transform: "translateY(0)" },
      ],
      {
        duration: ANIMATION_DURATION,
        easing: ANIMATION_EASING,
      }
    );

    const btn = contactForm.querySelector('button[type="submit"]');
    btn.textContent = "Envoyé !";
    setTimeout(() => {
      btn.textContent = "Envoyer";
      contactForm.reset();
    }, 1800);
  });
};

// Mascot Interaction Module
const initMascotInteraction = () => {
  const foxMascot = document.getElementById("fox-mascot");
  const accueilTitle = document.querySelector(".accueil-content h1");

  if (!foxMascot || !accueilTitle) return;

  accueilTitle.addEventListener("mouseenter", () => {
    foxMascot.style.opacity = "0.5";
    setTimeout(() => {
      foxMascot.style.opacity = "1";
    }, 350);
  });
};

// DateTime Module
const initDateTime = () => {
  const datetimeElement = document.querySelector(".datetime");
  if (!datetimeElement) {
    console.error("Element .datetime not found!");
    return;
  }

  const updateDateTime = () => {
    const now = new Date();
    const dateStr = now.toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
    datetimeElement.textContent = dateStr;
  };

  updateDateTime();
  setInterval(updateDateTime, 1000);
};

// Chatbot Module
const initChatbot = () => {
  const elements = {
    toggle: document.getElementById("chatbot-toggle"),
    window: document.getElementById("chatbot-window"),
    close: document.getElementById("chatbot-close"),
    clear: document.getElementById("chatbot-clear"),
    messages: document
      .getElementById("chatbot-window")
      ?.querySelector(".chatbot-messages"),
    input: document
      .getElementById("chatbot-window")
      ?.querySelector(".chatbot-input input"),
    sendButton: document
      .getElementById("chatbot-window")
      ?.querySelector(".chatbot-input button"),
  };

  if (!Object.values(elements).every(Boolean)) {
    console.error("One or more chatbot elements not found!");
    return;
  }

  // Historique de la conversation
  let chatHistory = [
    { role: 'assistant', content: 'Bonjour, je suis à votre disposition pour répondre à vos questions sur RedFox.' }
  ];

  const handleOpenChat = () => {
    elements.window.classList.remove("hidden", "closing");
    elements.window.classList.add("opening");
    elements.toggle.classList.add("hidden");

    elements.window.addEventListener(
      "animationend",
      () => {
        elements.window.classList.remove("opening");
      },
      { once: true }
    );
  };

  const handleCloseChat = () => {
    elements.window.classList.remove("opening");
    elements.window.classList.add("closing");

    elements.window.addEventListener(
      "animationend",
      () => {
        elements.window.classList.add("hidden");
        elements.window.classList.remove("closing");
        elements.toggle.classList.remove("hidden");
      },
      { once: true }
    );
  };

  const handleClearChat = () => {
    // Garder uniquement le message d'accueil dans l'affichage
    elements.messages.innerHTML = '<div class="message bot">Bonjour, je suis à votre disposition pour répondre à vos questions sur RedFox.</div>';
    // Réinitialiser l'historique
    chatHistory = [
      { role: 'assistant', content: 'Bonjour, je suis à votre disposition pour répondre à vos questions sur RedFox.' }
    ];
  };

  // URL de votre fonction Cloud
  const CLOUD_FUNCTION_URL = 'https://cloudfunc-llm-994261001875.europe-west9.run.app';

  // Fonction pour ajouter un message au chat (affichage et historique)
  const addMessage = (content, isBot = false) => {
    const role = isBot ? 'bot' : 'user'; // Correction: utiliser 'bot' au lieu de 'assistant'
    const messageDiv = createElement('div', `message ${role}`);
    messageDiv.textContent = content;
    elements.messages.appendChild(messageDiv);
    // Faire défiler vers le bas pour voir le nouveau message
    elements.messages.scrollTop = elements.messages.scrollHeight;

    // Ajouter à l'historique
    chatHistory.push({ role, content });
  }

  // Fonction pour envoyer un message au LLM via la fonction Cloud
  const sendMessageToLLM = async (message) => {
    // Ajouter un indicateur de chargement
    const loadingDiv = createElement('div', 'message bot loading');
    loadingDiv.textContent = 'Je réfléchis ...';
    elements.messages.appendChild(loadingDiv);
    elements.messages.scrollTop = elements.messages.scrollHeight; // Scroll after adding loading

    // Préparer l'historique pour l'envoi
    const historyToSend = chatHistory.slice(); // Copie pour ne pas modifier l'original ici

    try {
      const response = await fetch(CLOUD_FUNCTION_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        // Envoyer le dernier message et l'historique
        body: JSON.stringify({ message: message, history: historyToSend })
      });

      // Supprimer l'indicateur de chargement dès que la réponse arrive
      if (elements.messages.contains(loadingDiv)) {
        elements.messages.removeChild(loadingDiv);
      }

      if (!response.ok) {
        // Gérer les erreurs HTTP (ex: 404, 500)
        let errorMsg = `Erreur HTTP ${response.status}: ${response.statusText}`;
        try {
          const errorData = await response.json();
          errorMsg = errorData.error || errorMsg;
        } catch (e) { /* Ignorer si le corps n'est pas JSON */ }
        addMessage(`Désolé, une erreur serveur est survenue (${response.status}). Veuillez réessayer plus tard.`, true);
        console.error('Erreur serveur:', errorMsg);
        return; // Arrêter le traitement ici
      }

      const data = await response.json();

      if (data.response) {
        // Ajouter la réponse du LLM (addMessage l'ajoute aussi à l'historique)
        addMessage(data.response, true);
      } else {
        // Gérer le cas où la réponse est OK mais sans contenu attendu
        addMessage("Désolé, je n'ai pas pu obtenir de réponse valide. Veuillez réessayer.", true);
        console.error('Erreur: Réponse invalide ou manquante', data);
      }

    } catch (error) {
      // Supprimer l'indicateur de chargement si toujours présent (ex: erreur réseau)
      if (elements.messages.contains(loadingDiv)) {
          elements.messages.removeChild(loadingDiv);
      }
      // Afficher un message d'erreur générique pour les problèmes de connexion/fetch
      addMessage("Désolé, une erreur de communication s'est produite. Veuillez vérifier votre connexion et réessayer.", true);
      console.error('Erreur de communication:', error);
    }
  }

  // Gérer l'envoi de message (bouton ou touche Entrée)
  const handleSendMessage = () => {
    const message = elements.input.value.trim();
    if (message) {
      // Ajouter le message de l'utilisateur (addMessage l'ajoute aussi à l'historique)
      addMessage(message);

      // Envoyer au LLM (avec l'historique mis à jour)
      sendMessageToLLM(message);

      // Effacer le champ de saisie
      elements.input.value = '';
    }
  }

  // Event Listeners
  elements.toggle.addEventListener("click", handleOpenChat);
  elements.close.addEventListener("click", handleCloseChat);
  elements.clear.addEventListener("click", handleClearChat);
  elements.sendButton.addEventListener("click", handleSendMessage);
  elements.input.addEventListener("keypress", (event) => {
    if (event.key === "Enter") handleSendMessage();
  });

  // Afficher le message d'accueil initial (déjà dans l'historique)
  elements.messages.innerHTML = `<div class="message bot">${chatHistory[0].content}</div>`;
};

// Sidebar Toggle Module
const initSidebarToggle = () => {
  const sidebar = document.querySelector(".sidebar-definitions");
  const toggleButton = document.querySelector(".sidebar-toggle");

  if (!sidebar || !toggleButton) {
    console.error("Sidebar or toggle button not found!");
    return;
  }

  // Ouvre/ferme le menu au clic sur le bouton
  toggleButton.addEventListener("click", (e) => {
    e.stopPropagation();
    const isOpen = sidebar.classList.toggle("open");
    toggleButton.setAttribute("aria-expanded", isOpen);
    toggleButton.classList.toggle("open", isOpen);

    // Hide CTA when sidebar is opened for the first time
    if (isOpen) {
      hideSidebarCTA();
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
  });

  // Ferme le menu si clic en dehors du sidebar ET du bouton
  function handleClickOutside(e) {
    if (!sidebar.contains(e.target) && !toggleButton.contains(e.target)) {
      sidebar.classList.remove("open");
      toggleButton.classList.remove("open");
      toggleButton.setAttribute("aria-expanded", "false");
      document.removeEventListener("mousedown", handleClickOutside);
    }
  }
};

// Accessibility Module
const initAccessibility = () => {
  document.querySelectorAll("a, button, input, textarea").forEach((el) => {
    el.setAttribute("tabindex", "0");
  });
};

// === Theme Switcher ===
const initThemeSwitcher = () => {
  const themeToggle = document.getElementById('theme-toggle');
  const themeIcon = themeToggle?.querySelector('.theme-icon');
  const body = document.body;

  // Applique le thème sauvegardé
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme === 'light') {
    body.classList.add('light-theme');
    if (themeIcon) themeIcon.textContent = '☀️';
  } else {
    body.classList.remove('light-theme');
    if (themeIcon) themeIcon.textContent = '🌙';
  }

  themeToggle?.addEventListener('click', () => {
    body.classList.toggle('light-theme');
    const isLight = body.classList.contains('light-theme');
    if (themeIcon) themeIcon.textContent = isLight ? '☀️' : '🌙';
    localStorage.setItem('theme', isLight ? 'light' : 'dark');
    console.log('Thème actuel :', isLight ? 'light' : 'dark');
  });

  // Ajoute une transition douce sur le body
  body.style.transition = 'background 0.4s, color 0.4s';
};

// Projects Expansion Module
const initProjectsExpansion = () => {
  const projectSummaries = document.querySelectorAll('.project-summary');

  projectSummaries.forEach(summary => {
    const toggleProject = () => {
      const projectId = summary.getAttribute('data-project');
      const details = document.getElementById(`project-${projectId}`);

      if (!details) return;

      // Check if this project is already active
      const isActive = summary.classList.contains('active');

      // Close all project details first (accordion behavior)
      document.querySelectorAll('.project-summary').forEach(s => {
        s.classList.remove('active');
        s.setAttribute('aria-expanded', 'false');
      });
      document.querySelectorAll('.project-details').forEach(d => {
        d.classList.remove('active');
        d.setAttribute('aria-hidden', 'true');
      });

      // Then open the clicked one if it wasn't already open
      if (!isActive) {
        summary.classList.add('active');
        details.classList.add('active');
        summary.setAttribute('aria-expanded', 'true');
        details.setAttribute('aria-hidden', 'false');
      }
    };

  // Add click event
  summary.addEventListener('click', toggleProject);

  // Add keyboard support
  summary.addEventListener('keydown', (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleProject();
    }
  });
 });
};

// Projects Filtering Module
const initProjectsFiltering = () => {
  const searchInput = document.getElementById('project-search');
  const resultsCount = document.getElementById('results-count');

  // Sélecteurs pour les projets (support des deux pages + featured projects)
  const projectItems = document.querySelectorAll('.project-item, .featured-project-card');

  if (!searchInput || !resultsCount || !projectItems.length) {
    return; // Pas de filtres sur cette page
  }

  let currentSearchTerm = '';

  // Fonction pour normaliser le texte (enlever accents, minuscules)
  const normalizeText = (text) => {
    return text.toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');
  };

  // Fonction pour filtrer les projets
  const filterProjects = () => {
    let visibleCount = 0;
    let featuredVisibleCount = 0;
    let regularVisibleCount = 0;

    projectItems.forEach(project => {
      const searchText = project.getAttribute('data-search-text') || '';

      // Vérifier le filtre de recherche
      const normalizedSearchText = normalizeText(searchText);
      const normalizedSearchTerm = normalizeText(currentSearchTerm);
      const matchesSearch = currentSearchTerm === '' ||
        normalizedSearchText.includes(normalizedSearchTerm);

      // Afficher/masquer le projet
      if (matchesSearch) {
        project.classList.remove('filtered-out');
        visibleCount++;

        // Count featured vs regular projects
        if (project.classList.contains('featured-project-card')) {
          featuredVisibleCount++;
        } else {
          regularVisibleCount++;
        }
      } else {
        project.classList.add('filtered-out');
      }
    });

    // Hide/show sections based on visible projects
    const featuredSection = document.querySelector('.featured-projects-section');
    const allProjectsSection = document.querySelector('.all-projects-section');

    if (featuredSection) {
      featuredSection.style.display = featuredVisibleCount > 0 ? 'block' : 'none';
    }

    if (allProjectsSection) {
      allProjectsSection.style.display = regularVisibleCount > 0 ? 'block' : 'none';
    }

    // Mettre à jour le compteur
    const projectText = visibleCount === 1 ? 'projet affiché' : 'projets affichés';
    resultsCount.textContent = `${visibleCount} ${projectText}`;
  };

  // Gestionnaire pour la recherche textuelle
  const handleSearch = (event) => {
    currentSearchTerm = event.target.value.trim();
    filterProjects();
  };

  // Ajouter les écouteurs d'événements
  searchInput.addEventListener('input', handleSearch);
  searchInput.addEventListener('keyup', handleSearch);

  // Initialiser le filtrage
  filterProjects();
};

// Sidebar CTA Module
const initSidebarCTA = () => {
  const cta = document.getElementById('sidebar-cta');

  if (!cta) {
    return; // CTA not present on this page
  }

  // Check if user has already seen/interacted with the sidebar
  const hasSeenSidebar = localStorage.getItem('sidebarSeen');

  if (hasSeenSidebar === 'true') {
    cta.classList.add('hidden');
    return;
  }

  // Show CTA after a short delay
  setTimeout(() => {
    cta.style.opacity = '1';
  }, 1500);

  // Auto-hide after 10 seconds if not interacted with
  setTimeout(() => {
    if (!cta.classList.contains('hidden')) {
      cta.classList.add('hidden');
    }
  }, 10000);
};

// Function to hide CTA and mark as seen
const hideSidebarCTA = () => {
  const cta = document.getElementById('sidebar-cta');

  if (cta && !cta.classList.contains('hidden')) {
    cta.classList.add('hidden');
    localStorage.setItem('sidebarSeen', 'true');
  }
};

// === Projet dynamique depuis projects.js ===

function renderProjects() {
  if (typeof PROJECTS === 'undefined') return;

  // Sélecteurs pour les deux pages
  const featuredGrid = document.querySelector('.featured-projects-grid');
  const allProjectsList = document.querySelector('.projects-list, .projects-grid');

  if (!featuredGrid || !allProjectsList) return;

  // Nettoyer les conteneurs
  featuredGrid.innerHTML = '';
  allProjectsList.innerHTML = '';

  // Séparer projets mis en avant et autres
  const featured = PROJECTS.filter(p => p.featured);
  const others = PROJECTS.filter(p => !p.featured);

  // Générer les projets mis en avant (max 3)
  featured.slice(0, 3).forEach(project => {
    featuredGrid.appendChild(createProjectCard(project, true));
  });

  // Générer les autres projets
  others.forEach(project => {
    allProjectsList.appendChild(createProjectCard(project, false));
  });
}

function createProjectCard(project, isFeatured) {
  const card = document.createElement('div');
  card.className = isFeatured ? 'featured-project-card' : 'project-item';
  card.setAttribute('data-tags', project.tags.join(','));
  card.setAttribute('data-search-text', `${project.id} ${project.title} ${project.description}`);
  card.setAttribute('data-featured', project.featured ? 'true' : 'false');

  // Structure HTML différente selon le type
  if (isFeatured) {
    card.innerHTML = `
      <h3>${project.title}</h3>
      <div class="featured-project-image" role="img" aria-label="${project.pixelartAlt}"></div>
      <p class="featured-project-description">${project.description}</p>
      <div class="featured-project-tech">
        ${project.tags.map(tag => `<span class="tech-tag">${tag}</span>`).join('')}
      </div>
      <a href="${project.link}" class="featured-project-link">Voir le projet</a>
    `;
  } else {
    // Create collapsible card structure for non-featured projects
    const projectIcon = project.icon || (project.link.includes('.html') ? 'document-icon' : '');
    const shortDescription = project.description.length > 100 ?
      project.description.substring(0, 100) + '...' : project.description;

    card.innerHTML = `
      <div class="project-summary" data-project="${project.id}" aria-expanded="false" role="button" tabindex="0" aria-controls="project-${project.id}">
        <div class="project-pixelart ${projectIcon}"></div>
        <div class="project-summary-content">
          <h3>${project.title}</h3>
          <p class="project-summary-description">${shortDescription}</p>
        </div>
        <span class="expand-arrow" aria-hidden="true">▶</span>
      </div>
      <div class="project-details" id="project-${project.id}" aria-hidden="true">
        <div class="project-image"></div>
        <div class="project-info">
          <h3>${project.title}</h3>
          <p class="project-description">${project.description}</p>
          <div class="project-tech">
            ${project.tags.map(tag => `<span class="tech-tag">${tag}</span>`).join('')}
          </div>
          <div class="project-meta">
            ${project.start ? `
              <div class="project-date">
                <span class="date-label">Début:</span>
                <span class="date-value">${project.start}</span>
              </div>
            ` : ''}
            ${project.end ? `
              <div class="project-date">
                <span class="date-label">Fin:</span>
                <span class="date-value">${project.end}</span>
              </div>
            ` : ''}
          </div>
          <a href="${project.link}" class="project-link">Voir le projet</a>
        </div>
      </div>
    `;
  }
  return card;
}

// Appeler le rendu au chargement du DOM
window.addEventListener('DOMContentLoaded', renderProjects);

// Initialize all modules
document.addEventListener("DOMContentLoaded", () => {
  initNavigation();
  initSectionAnimations();
  initSkillsAnimation();
  initContactForm();
  initMascotInteraction();
  initDateTime();
  initChatbot();
  initAccessibility();
  initSidebarToggle();
  initSidebarCTA();
  initThemeSwitcher();
  initProjectsExpansion();
  initProjectsFiltering();
});
