const PROJECTS = [
  {
    id: "cyberrating",
    title: "Cyber-Rating du Groupe Orange",
    subtitle: "Notations de cybersécurité",
    description: "Amélioration du service de notation utilisé par la Direction de la sécurité d'Orange à l'attention des différentes filiales et entités du Groupe.",
    tags: ["Python (Django & Pandas)", "IA Gen", "Machine Learning", "Vectors DB", "Orchestration Multi-Agents", "Context Engineering", "Prompt Engineering"],
    featured: true,
    start: "07/2025",
    end: "En cours",
    link: "/pages/projets.html",
    pixelartAlt: "Pixel art icon for CyberRating project"
  },
  {
    id: "semarchychatbot",
    title: "Assistant IA",
    subtitle: "Chatbot interne - Orange Business",
    description: "Conception / Lead Dev sur un projet de chatbot à usage transverse, multi-capable et multi-sources. Développement d'un RAG doté de connecteurs Cloud. Conception d'une solution de webscrapping à exécution régulière et connectée au RAG.",
    tags: ["Python (Streamlit & FastAPI)", "IA Gen", "RAG", "Context Engineering", "Azure", "GCP", "AWS", "Langchain", "Webscrapping"],
    featured: true,
    start: "10/2024",
    end: "07/2025",
    link: "/projets/semarchychatbot.html",
    pixelartAlt: "Pixel art icon for SemarchyChatbot project"
  },
  {
    id: "idmc",
    title: "Extracteur de données d'ETL",
    subtitle: "Métadonnées IDMC / Informatika",
    description: "Développement d'un service d'extraction de données issues de l'ETL IDMC dans le cadre de la migration depuis Power Center et nécessitant la prise en charge de métadonnées tirées de workflow clients. Projet fortement orienté SQL.",
    tags: ["Python", "SQL", "ETL", "IDMC", "Talend", "SQLite"],
    featured: false,
    start: "09/2024",
    end: "06/2025",
    link: "projets/idmc.html",
    pixelartAlt: "Pixel art icon for IDMC project"
  },
  {
    id: "liveintelligence-chatbot",
    title: "Chatbot pour collectivités",
    subtitle: "Backend basé sur FastAPI",
    description: "Développement d'un backend FastAPI pour une webapp de chatbot dédié aux collectivités publiques, basé sur l'IA générative. API REST complète avec authentification, gestion des sessions et intégration de modèles de langage.",
    tags: ["Python (FastAPI)", "IA Gen", "API REST", "Authentification", "PostgreSQL", "Docker", "OpenAI", "Langchain"],
    featured: false,
    start: "06/2025",
    end: "07/2025",
    link: "projets/liveintelligence-chatbot.html",
    pixelartAlt: "Pixel art icon for LiveIntelligenceChatbot project"
  }
];