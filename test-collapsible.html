<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Collapsible Cards</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div style="padding: 2rem; max-width: 800px; margin: 0 auto;">
        <h1>Test des cartes collapsibles</h1>
        
        <h2>Mode Liste (index.html)</h2>
        <div class="projects-list">
            <!-- Les projets seront générés ici -->
        </div>
        
        <h2>Mode Grille (projets.html)</h2>
        <div class="projects-grid">
            <!-- Les projets seront générés ici -->
        </div>
        
        <div style="margin-top: 2rem;">
            <button onclick="testAccessibility()">Test Accessibilité</button>
            <button onclick="testResponsive()">Test Responsive</button>
        </div>
        
        <div id="test-results" style="margin-top: 1rem; padding: 1rem; background: #f0f0f0; border-radius: 4px; display: none;">
            <h3>Résultats des tests</h3>
            <ul id="results-list"></ul>
        </div>
    </div>

    <script src="projects.js"></script>
    <script src="script.js"></script>
    <script>
        // Test functions
        function testAccessibility() {
            const results = [];
            const summaries = document.querySelectorAll('.project-summary');
            
            summaries.forEach((summary, index) => {
                // Test ARIA attributes
                if (summary.hasAttribute('aria-expanded')) {
                    results.push(`✓ Carte ${index + 1}: aria-expanded présent`);
                } else {
                    results.push(`✗ Carte ${index + 1}: aria-expanded manquant`);
                }
                
                // Test tabindex
                if (summary.hasAttribute('tabindex')) {
                    results.push(`✓ Carte ${index + 1}: tabindex présent`);
                } else {
                    results.push(`✗ Carte ${index + 1}: tabindex manquant`);
                }
                
                // Test role
                if (summary.hasAttribute('role')) {
                    results.push(`✓ Carte ${index + 1}: role présent`);
                } else {
                    results.push(`✗ Carte ${index + 1}: role manquant`);
                }
            });
            
            showResults(results);
        }
        
        function testResponsive() {
            const results = [];
            const listContainer = document.querySelector('.projects-list');
            const gridContainer = document.querySelector('.projects-grid');
            
            if (listContainer && listContainer.children.length > 0) {
                results.push(`✓ Mode liste: ${listContainer.children.length} projets affichés`);
            } else {
                results.push(`✗ Mode liste: aucun projet affiché`);
            }
            
            if (gridContainer && gridContainer.children.length > 0) {
                results.push(`✓ Mode grille: ${gridContainer.children.length} projets affichés`);
            } else {
                results.push(`✗ Mode grille: aucun projet affiché`);
            }
            
            showResults(results);
        }
        
        function showResults(results) {
            const resultsDiv = document.getElementById('test-results');
            const resultsList = document.getElementById('results-list');
            
            resultsList.innerHTML = '';
            results.forEach(result => {
                const li = document.createElement('li');
                li.textContent = result;
                li.style.color = result.startsWith('✓') ? 'green' : 'red';
                resultsList.appendChild(li);
            });
            
            resultsDiv.style.display = 'block';
        }
    </script>
</body>
</html>
